import { z } from "zod";

// Candidate registration schema fields
const candidateFields = {
  name: z.string().min(2, "Name must be at least 2 characters"),
  professionalTitle: z
    .string()
    .min(2, "Professional title must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
    ),
  passwordConfirmation: z.string(),
  role: z.literal("candidate"),
};

// Candidate registration schema
export const candidateRegisterSchema = z
  .object(candidateFields)
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords do not match",
    path: ["passwordConfirmation"],
  });
