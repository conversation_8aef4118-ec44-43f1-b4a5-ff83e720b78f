import { loginSchema } from "@/schemas/auth/auth-schemas";
import { type LoginCredentials } from "@/types/auth";
import {
  Button,
  Divider,
  Group,
  PasswordInput,
  TextInput,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodResolver } from "mantine-form-zod-resolver";
import { FaEnvelope, FaLock } from "react-icons/fa";
import { Link } from "react-router";

export default function LoginForm() {
  // const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
    validate: zodResolver(loginSchema),
  });

  const handleSubmit = async (values: LoginCredentials) => {
    console.log(values);
  };

  return (
    <form
      noValidate
      onSubmit={form.onSubmit(handleSubmit)}
      className="relative flex flex-col gap-4"
    >
      {/* <LoadingOverlay visible={isSubmitting} overlayProps={{ blur: 2 }} /> */}
      <TextInput
        label="Email"
        placeholder="<EMAIL>"
        required
        leftSection={<FaEnvelope />}
        {...form.getInputProps("email")}
      />

      <PasswordInput
        label="Password"
        placeholder="Your password"
        required
        leftSection={<FaLock />}
        {...form.getInputProps("password")}
      />

      <Group justify="space-between" mt="md">
        <Link
          to="/auth/forgot-password"
          className="text-sm text-blue-600 hover:underline"
        >
          Forgot password?
        </Link>
      </Group>

      <Button
        type="submit"
        fullWidth
        mt="xl"
        // loading={isSubmitting}
      >
        Login
      </Button>

      <Divider label="Don't have an account?" labelPosition="center" my="lg" />

      <Button component={Link} to="/auth/register" variant="outline" fullWidth>
        Create Account
      </Button>
    </form>
  );
}
