import JobCard from "@/components/employer/manage-jobs/JobCard";
import { <PERSON><PERSON>ontainer, PageHeading } from "@/design-system/components";
import { useJobs } from "@/hooks/employer";
import {
  ActionIcon,
  Badge,
  Box,
  Button,
  Card,
  Divider,
  Grid,
  Group,
  Pagination,
  SegmentedControl,
  Select,
  Text,
  TextInput,
  Title,
  useMantineColorScheme,
  useMantineTheme,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useState } from "react";
import {
  FaFilter,
  FaPlus,
  FaSearch,
  FaSortAmountDown,
  FaSortAmountUp,
  FaThLarge,
  FaThList,
} from "react-icons/fa";
import { Link } from "react-router";

export default function EmployerManageJobsPage() {
  // State for search and filters
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<string | null>("newest");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Responsive helpers
  const theme = useMantineTheme();
  const isMobile = useMediaQuery(`(max-width: ${theme.breakpoints.sm})`);

  // Theme helpers
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";

  // Get jobs data from the service
  const { jobs } = useJobs();

  // Ensure jobs is always an array
  const safeJobs = jobs || [];

  // Filter jobs based on search and status
  const filteredJobs = safeJobs.filter((job) => {
    const matchesStatus = statusFilter ? job.status === statusFilter : true;
    const matchesSearch = job.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Sort jobs
  const sortedJobs = [...filteredJobs].sort((a, b) => {
    if (sortBy === "newest")
      return (
        new Date(b.postedDate).getTime() - new Date(a.postedDate).getTime()
      );
    if (sortBy === "oldest")
      return (
        new Date(a.postedDate).getTime() - new Date(b.postedDate).getTime()
      );
    if (sortBy === "title-asc") return a.title.localeCompare(b.title);
    if (sortBy === "title-desc") return b.title.localeCompare(a.title);
    return 0;
  });

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Manage Jobs" }]}
      variant="employer"
      className="pb-12"
    >
      {/* Hero Section */}
      <Box
        className="mb-8 rounded-lg px-4 py-8 md:px-6 md:py-10"
        mx={{ base: -4, sm: -6 }}
        style={{
          background: isDark
            ? "linear-gradient(to right, #1a1b2e, #141b2d, #1a1b2e)"
            : "linear-gradient(to right, #f0f4ff, #e6f0ff, #f0f4ff)",
        }}
      >
        <PageHeading
          title="Manage Jobs"
          subtitle="View, edit, and track your job postings"
          className="mb-6 text-center md:mb-8"
          variant="employer"
        />

        <Card
          className="mx-auto max-w-4xl shadow-sm"
          withBorder={false}
          p={{ base: "xs", sm: "md" }}
        >
          <Group wrap="nowrap" align="flex-start">
            <TextInput
              placeholder="Search by job title"
              className="min-w-0 flex-grow"
              leftSection={<FaSearch size={16} />}
              size={isMobile ? "sm" : "md"}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Group wrap="nowrap" gap="xs">
              <Select
                placeholder="Status"
                data={[
                  { value: "Active", label: "Active" },
                  { value: "Pending", label: "Pending" },
                  { value: "Closed", label: "Closed" },
                ]}
                leftSection={<FaFilter size={16} />}
                size={isMobile ? "sm" : "md"}
                value={statusFilter}
                onChange={setStatusFilter}
                clearable
              />
              <Link to="/employer/create-job">
                <Button
                  size={isMobile ? "sm" : "md"}
                  leftSection={<FaPlus size={14} />}
                >
                  Post Job
                </Button>
              </Link>
            </Group>
          </Group>
        </Card>
      </Box>

      {/* Results and View Controls */}
      <Card withBorder radius="md" className="mb-6 shadow-sm" p="md">
        <Group justify="space-between" mb="sm" wrap="nowrap" align="center">
          <Group wrap="nowrap" gap="xs">
            <Text fw={500} size="sm" className="sm:text-base">
              Results
            </Text>
            <Text c="dimmed" size="xs" className="hidden sm:block">
              Showing {sortedJobs.length} job
              {sortedJobs.length !== 1 ? "s" : ""}
            </Text>
          </Group>

          <Group gap="md">
            <Select
              placeholder="Sort by"
              data={[
                { value: "newest", label: "Newest First" },
                { value: "oldest", label: "Oldest First" },
                { value: "title-asc", label: "Title (A-Z)" },
                { value: "title-desc", label: "Title (Z-A)" },
              ]}
              leftSection={
                sortBy?.includes("desc") ? (
                  <FaSortAmountDown size={14} />
                ) : (
                  <FaSortAmountUp size={14} />
                )
              }
              size="sm"
              value={sortBy}
              onChange={setSortBy}
              className="hidden w-40 sm:block"
            />

            <SegmentedControl
              value={viewMode}
              onChange={(value) => setViewMode(value as "grid" | "list")}
              data={[
                {
                  value: "grid",
                  label: (
                    <div className="flex items-center gap-2">
                      <FaThLarge size={14} />
                      <span className="hidden sm:inline">Grid</span>
                    </div>
                  ),
                },
                {
                  value: "list",
                  label: (
                    <div className="flex items-center gap-2">
                      <FaThList size={14} />
                      <span className="hidden sm:inline">List</span>
                    </div>
                  ),
                },
              ]}
              size="xs"
            />
          </Group>
        </Group>

        <Divider mb="sm" />

        {/* Filter badges */}
        {(searchQuery || statusFilter) && (
          <Group mt="xs">
            {searchQuery && (
              <Badge
                variant="light"
                color="blue"
                rightSection={
                  <ActionIcon
                    size="xs"
                    color="blue"
                    variant="transparent"
                    onClick={() => setSearchQuery("")}
                  >
                    ×
                  </ActionIcon>
                }
              >
                Search: {searchQuery}
              </Badge>
            )}
            {statusFilter && (
              <Badge
                variant="light"
                color={
                  statusFilter === "Active"
                    ? "blue"
                    : statusFilter === "Pending"
                      ? "yellow"
                      : "red"
                }
                rightSection={
                  <ActionIcon
                    size="xs"
                    color={
                      statusFilter === "Active"
                        ? "blue"
                        : statusFilter === "Pending"
                          ? "yellow"
                          : "red"
                    }
                    variant="transparent"
                    onClick={() => setStatusFilter(null)}
                  >
                    ×
                  </ActionIcon>
                }
              >
                Status: {statusFilter}
              </Badge>
            )}
          </Group>
        )}
      </Card>

      {/* Jobs List */}
      {sortedJobs.length === 0 ? (
        <Card withBorder radius="md" className="p-8 text-center shadow-sm">
          <Title order={3} className="mb-2 text-gray-700">
            No jobs found
          </Title>
          <Text c="dimmed" className="mb-6">
            Try adjusting your search or filters to find what you&apos;re
            looking for.
          </Text>
          <Link to="/employer/create-job">
            <Button leftSection={<FaPlus size={14} />}>Post a New Job</Button>
          </Link>
        </Card>
      ) : (
        <Grid gutter="lg">
          {sortedJobs.map((job) => (
            <JobCard key={job.id} job={job} viewMode={viewMode} />
          ))}
        </Grid>
      )}

      {/* Pagination */}
      {sortedJobs.length > 0 && (
        <div className="mt-8 flex justify-center">
          <Pagination
            total={Math.ceil(sortedJobs.length / 10)}
            radius="md"
            size={isMobile ? "sm" : "md"}
          />
        </div>
      )}
    </PageContainer>
  );
}
