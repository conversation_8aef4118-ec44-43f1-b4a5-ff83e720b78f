import { candidateRegisterSchema } from "@/schemas/auth/candidate-register-schema";
import { type RegisterData } from "@/types/auth";
import { Button, PasswordInput, TextInput } from "@mantine/core";
import { useForm } from "@mantine/form";
import { zodR<PERSON>olver } from "mantine-form-zod-resolver";
import { FaBriefcase, FaEnvelope, FaLock, FaUser } from "react-icons/fa";
import { Link } from "react-router";

export default function CandidateRegisterForm() {
  // const [isLoading, setIsLoading] = useState(false);

  const form = useForm({
    initialValues: {
      name: "",
      professionalTitle: "",
      email: "",
      password: "",
      passwordConfirmation: "",
      role: "candidate" as const,
    },
    validate: zodResolver(candidateRegisterSchema),
  });

  const handleSubmit = async (values: RegisterData) => {
    console.log(values);
  };

  return (
    <div className="relative">
      {/* <LoadingOverlay visible={isLoading} overlayProps={{ blur: 2 }} /> */}

      <form
        noValidate
        className="flex flex-col gap-4"
        onSubmit={form.onSubmit(handleSubmit)}
      >
        <TextInput
          label="Full Name"
          placeholder="John Doe"
          required
          leftSection={<FaUser />}
          {...form.getInputProps("name")}
        />

        <TextInput
          label="Professional Title"
          placeholder="e.g. Software Engineer, Marketing Manager"
          required
          leftSection={<FaBriefcase />}
          {...form.getInputProps("professionalTitle")}
        />

        <TextInput
          label="Email"
          placeholder="<EMAIL>"
          required
          leftSection={<FaEnvelope />}
          {...form.getInputProps("email")}
        />

        <PasswordInput
          label="Password"
          placeholder="Your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("password")}
        />

        <PasswordInput
          label="Confirm Password"
          placeholder="Confirm your password"
          required
          leftSection={<FaLock />}
          {...form.getInputProps("passwordConfirmation")}
        />

        <Button
          type="submit"
          fullWidth
          mt="xl"
          // loading={isLoading}
        >
          Create Account
        </Button>

        <div className="mt-4 text-center">
          <Link
            to="/auth/login"
            className="text-sm text-blue-600 hover:underline"
          >
            Already have an account? Login
          </Link>
        </div>
      </form>
    </div>
  );
}
