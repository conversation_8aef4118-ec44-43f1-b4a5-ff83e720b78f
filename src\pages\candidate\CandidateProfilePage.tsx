"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/design-system/components";
import useCandidateProfile from "@/hooks/candidate/use-candidate-profile";
import candidateApi from "@/services/candidate-api";
import { useAuthStore } from "@/stores/auth-store";
import {
  ActionIcon,
  Avatar,
  Badge,
  Button,
  Card,
  Checkbox,
  Divider,
  FileInput,
  Grid,
  Group,
  Modal,
  RingProgress,
  Select,
  Tabs,
  Text,
  TextInput,
  Textarea,
  Title,
  Tooltip,
  useMantineColorScheme,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { useDisclosure } from "@mantine/hooks";
import { notifications } from "@mantine/notifications";
import { useEffect, useState } from "react";
import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaCamera,
  FaCheck,
  FaEnvelope,
  FaGithub,
  FaGlobe,
  FaGraduationCap,
  FaLinkedin,
  FaMapMarkerAlt,
  FaPhone,
  FaPlus,
  <PERSON>aSave,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaStar,
  FaTrash,
  FaUpload,
  FaUser,
} from "react-icons/fa";
import { Link } from "react-router";

// Types for form data
interface EducationItem {
  _id?: string;
  degree: string;
  institution: string;
  year: string;
}

interface SkillItem {
  _id?: string;
  name: string;
  level: string;
}

interface ExperienceItem {
  _id?: string;
  position: string;
  company: string;
  startDate?: Date | null;
  endDate?: Date | null;
  current?: boolean;
  description: string;
  duration?: string; // For display purposes
}

export default function CandidateProfilePage() {
  const [activeTab, setActiveTab] = useState<string | null>("personal");
  const { colorScheme } = useMantineColorScheme();
  const isDark = colorScheme === "dark";
  const { user } = useAuthStore();

  // Helper function to get theme classes
  const getThemeClasses = (
    lightClasses: string,
    darkClasses: string,
  ): string => {
    return isDark ? darkClasses : lightClasses;
  };

  // Use our custom hook for profile management
  const {
    profile,
    isLoading,
    error,
    updateProfile,
    addSkill,
    removeSkill,
    addEducation,
    deleteEducation,
    addExperience,
    deleteExperience,
    uploadResume,
    refreshProfile,
  } = useCandidateProfile();

  // Modal states
  const [skillModalOpened, { open: openSkillModal, close: closeSkillModal }] =
    useDisclosure(false);
  const [
    experienceModalOpened,
    { open: openExperienceModal, close: closeExperienceModal },
  ] = useDisclosure(false);
  const [
    educationModalOpened,
    { open: openEducationModal, close: closeEducationModal },
  ] = useDisclosure(false);
  const [
    profileImageModalOpened,
    { open: openProfileImageModal, close: closeProfileImageModal },
  ] = useDisclosure(false);

  // Delete confirmation modals
  const [
    deleteSkillModalOpened,
    { open: openDeleteSkillModal, close: closeDeleteSkillModal },
  ] = useDisclosure(false);
  const [
    deleteExperienceModalOpened,
    { open: openDeleteExperienceModal, close: closeDeleteExperienceModal },
  ] = useDisclosure(false);
  const [
    deleteEducationModalOpened,
    { open: openDeleteEducationModal, close: closeDeleteEducationModal },
  ] = useDisclosure(false);

  // Items to delete (indexes)
  const [skillToDelete, setSkillToDelete] = useState<number | null>(null);
  const [experienceToDelete, setExperienceToDelete] = useState<number | null>(
    null,
  );
  const [educationToDelete, setEducationToDelete] = useState<number | null>(
    null,
  );

  // New item states
  const [newSkill, setNewSkill] = useState("");
  const [newEducation, setNewEducation] = useState<EducationItem>({
    degree: "",
    institution: "",
    year: "",
  });

  // Profile image state
  const [, setProfileImage] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string | null>(
    null,
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate profile completion percentage from the backend or use a default
  const profileCompletionPercentage = profile?.profileCompleteness || 0;

  // Initialize form with user data from auth store and profile data from backend
  const [formInitialized, setFormInitialized] = useState(false);

  const form = useForm<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    location: string;
    title: string;
    bio: string;
    linkedin: string;
    github: string;
    portfolio: string;
    skills: (string | SkillItem)[];
    education: EducationItem[];
    experience: ExperienceItem[];
  }>({
    initialValues: {
      firstName: user?.name?.split(" ")[0] || "",
      lastName: user?.name?.split(" ").slice(1).join(" ") || "",
      email: user?.email || "",
      phone: "",
      location: "",
      title: profile?.headline || "",
      bio: profile?.summary || "",
      linkedin: profile?.socialLinks?.linkedin || "",
      github: profile?.socialLinks?.github || "",
      portfolio: profile?.socialLinks?.website || "",
      skills: [],
      education: [],
      experience: [],
    },
  });

  // Update form values when profile data is loaded
  useEffect(() => {
    if (profile && !formInitialized) {
      // Update form with profile data
      form.setValues({
        firstName: user?.name?.split(" ")[0] || "",
        lastName: user?.name?.split(" ").slice(1).join(" ") || "",
        email: user?.email || "",
        phone: profile.phone || "",
        location: profile.location || "",
        title: profile.headline || "",
        bio: profile.summary || "",
        linkedin: profile.socialLinks?.linkedin || "",
        github: profile.socialLinks?.github || "",
        portfolio: profile.socialLinks?.website || "",
        skills:
          profile.skills?.map((skill: any) => {
            // Handle both string skills and object skills with level
            if (typeof skill === "string") {
              return skill;
            } else if (typeof skill === "object" && skill.name) {
              return {
                name: skill.name,
                level: skill.level || "intermediate",
              };
            } else {
              return typeof skill === "string"
                ? skill
                : { name: String(skill), level: "intermediate" };
            }
          }) || [],
        education:
          profile.education?.map((edu) => ({
            degree: edu.degree,
            institution: edu.institution,
            year: new Date(edu.startDate).getFullYear().toString(),
          })) || [],
        experience:
          profile.experience?.map((exp) => ({
            _id: exp._id,
            position: exp.position,
            company: exp.company,
            startDate: new Date(exp.startDate),
            endDate: exp.endDate ? new Date(exp.endDate) : null,
            current: exp.current || false,
            description: exp.description || "",
            duration: `${new Date(exp.startDate).getFullYear()} - ${exp.endDate ? new Date(exp.endDate).getFullYear() : "Present"}`,
          })) || [],
      });
      setFormInitialized(true);
    }
  }, [profile, user, formInitialized]);

  // Skill form validation
  const [isAddingSkill, setIsAddingSkill] = useState(false);
  const [skillError, setSkillError] = useState("");
  const [skillLevel, setSkillLevel] = useState<string>("intermediate");

  const validateSkill = () => {
    if (!newSkill.trim()) {
      setSkillError("Skill name is required");
      return false;
    }

    // Check if skill already exists
    const existingSkill = form.values.skills.find((s) =>
      typeof s === "string"
        ? s === newSkill.trim()
        : s.name === newSkill.trim(),
    );

    if (existingSkill) {
      setSkillError("This skill already exists in your profile");
      return false;
    }

    setSkillError("");
    return true;
  };

  const handleAddSkill = async () => {
    if (!validateSkill()) {
      return;
    }

    setIsAddingSkill(true);

    // Create a skill object with name and level
    const skillData = {
      name: newSkill.trim(),
      level: skillLevel,
    };

    const success = await addSkill(skillData);
    setIsAddingSkill(false);

    if (success) {
      // Update the form with the new skill
      form.setFieldValue("skills", [...form.values.skills, skillData]);
      setNewSkill("");
      setSkillLevel("intermediate"); // Reset to default
      setSkillError("");

      // Also refresh the profile in the background to get the server data
      refreshProfile();

      closeSkillModal();
      notifications.show({
        title: "Skill Added",
        message: `${skillData.name} has been added to your skills`,
        color: "green",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to add skill. Please try again.",
        color: "red",
      });
    }
  };

  // Experience form validation
  const [isAddingExperience, setIsAddingExperience] = useState(false);
  const [experienceErrors, setExperienceErrors] = useState({
    position: "",
    company: "",
    startDate: "",
    endDate: "",
    description: "",
  });

  // Use the existing ExperienceItem interface

  // Initialize with null dates and current flag
  const [newExperience, setNewExperience] = useState<ExperienceItem>({
    position: "",
    company: "",
    startDate: null,
    endDate: null,
    current: false,
    description: "",
  });

  const validateExperience = () => {
    const errors = {
      position: "",
      company: "",
      startDate: "",
      endDate: "",
      description: "",
    };
    let isValid = true;

    if (!newExperience.position.trim()) {
      errors.position = "Position is required";
      isValid = false;
    }

    if (!newExperience.company.trim()) {
      errors.company = "Company name is required";
      isValid = false;
    }

    if (!newExperience.startDate) {
      errors.startDate = "Start date is required";
      isValid = false;
    }

    if (!newExperience.current && !newExperience.endDate) {
      errors.endDate = "End date is required if not current position";
      isValid = false;
    }

    if (
      newExperience.startDate &&
      newExperience.endDate &&
      !newExperience.current
    ) {
      if (newExperience.startDate > newExperience.endDate) {
        errors.endDate = "End date cannot be before start date";
        isValid = false;
      }
    }

    // Check if start date is in the future
    if (newExperience.startDate) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (newExperience.startDate > today) {
        errors.startDate = "Start date cannot be in the future";
        isValid = false;
      }
    }

    setExperienceErrors(errors);
    return isValid;
  };

  const handleAddExperience = async () => {
    if (!validateExperience()) {
      return;
    }

    setIsAddingExperience(true);

    const experienceData = {
      position: newExperience.position,
      company: newExperience.company,
      startDate: newExperience.startDate
        ? newExperience.startDate.toISOString()
        : new Date().toISOString(),
      endDate: newExperience.current
        ? undefined
        : newExperience.endDate
          ? newExperience.endDate.toISOString()
          : undefined,
      current: newExperience.current,
      description: newExperience.description,
    };

    const success = await addExperience(experienceData);
    setIsAddingExperience(false);

    if (success) {
      // Format dates for display
      const startYear = newExperience.startDate
        ? newExperience.startDate.getFullYear()
        : new Date().getFullYear();
      const endDisplay = newExperience.current
        ? "Present"
        : newExperience.endDate
          ? newExperience.endDate.getFullYear()
          : "";
      const formattedDuration = `${startYear} - ${endDisplay}`;

      // Reset the form
      setNewExperience({
        position: "",
        company: "",
        startDate: null,
        endDate: null,
        current: false,
        description: "",
      });

      // Reset errors
      setExperienceErrors({
        position: "",
        company: "",
        startDate: "",
        endDate: "",
        description: "",
      });

      // Manually update the UI with the new experience
      const newExperienceItem: ExperienceItem = {
        position: experienceData.position,
        company: experienceData.company,
        startDate: newExperience.startDate,
        endDate: newExperience.endDate,
        current: newExperience.current,
        description: experienceData.description,
        duration: formattedDuration,
      };

      form.setFieldValue("experience", [
        ...form.values.experience,
        newExperienceItem,
      ]);

      // Also refresh the profile in the background to get the server data
      refreshProfile();

      closeExperienceModal();
      notifications.show({
        title: "Experience Added",
        message: `${newExperience.position} at ${newExperience.company} has been added`,
        color: "green",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to add experience. Please try again.",
        color: "red",
      });
    }
  };

  // Education form validation
  const [isAddingEducation, setIsAddingEducation] = useState(false);
  const [educationErrors, setEducationErrors] = useState({
    degree: "",
    institution: "",
    year: "",
  });

  const validateEducation = () => {
    const errors = {
      degree: "",
      institution: "",
      year: "",
    };
    let isValid = true;

    if (!newEducation.degree.trim()) {
      errors.degree = "Degree is required";
      isValid = false;
    }

    if (!newEducation.institution.trim()) {
      errors.institution = "Institution name is required";
      isValid = false;
    }

    if (!newEducation.year.trim()) {
      errors.year = "Year is required";
      isValid = false;
    } else {
      // Validate year format (YYYY)
      const yearRegex = /^\d{4}$/;
      if (!yearRegex.test(newEducation.year)) {
        errors.year = "Year should be in YYYY format";
        isValid = false;
      } else {
        // Check if year is valid
        const year = parseInt(newEducation.year);
        const currentYear = new Date().getFullYear();

        if (year > currentYear) {
          errors.year = "Year cannot be in the future";
          isValid = false;
        }

        if (year < 1900) {
          errors.year = "Please enter a year after 1900";
          isValid = false;
        }
      }
    }

    setEducationErrors(errors);
    return isValid;
  };

  const handleAddEducation = async () => {
    if (!validateEducation()) {
      return;
    }

    setIsAddingEducation(true);

    const educationData = {
      degree: newEducation.degree,
      institution: newEducation.institution,
      startDate: new Date(parseInt(newEducation.year), 0, 1).toISOString(),
      current: false,
    };

    const success = await addEducation(educationData);
    setIsAddingEducation(false);

    if (success) {
      // Reset the form
      setNewEducation({
        degree: "",
        institution: "",
        year: "",
      });

      // Reset errors
      setEducationErrors({
        degree: "",
        institution: "",
        year: "",
      });

      // Manually update the UI with the new education
      const newEducationItem = {
        degree: newEducation.degree,
        institution: newEducation.institution,
        year: newEducation.year,
      };

      form.setFieldValue("education", [
        ...form.values.education,
        newEducationItem,
      ]);

      // Also refresh the profile in the background to get the server data
      refreshProfile();

      closeEducationModal();
      notifications.show({
        title: "Education Added",
        message: `${newEducation.degree} from ${newEducation.institution} has been added`,
        color: "green",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to add education. Please try again.",
        color: "red",
      });
    }
  };

  // Handle confirming skill deletion
  const confirmDeleteSkill = (index: number) => {
    setSkillToDelete(index);
    openDeleteSkillModal();
  };

  // Handle removing a skill
  const handleRemoveSkill = async () => {
    if (skillToDelete === null) return;

    const index = skillToDelete;
    const skillToRemove = form.values.skills[index];
    setIsSubmitting(true);

    // Extract the skill name if it's an object
    const skillName =
      typeof skillToRemove === "string" ? skillToRemove : skillToRemove.name;

    const success = await removeSkill(skillName);
    setIsSubmitting(false);

    if (success) {
      const updatedSkills = [...form.values.skills];
      updatedSkills.splice(index, 1);
      form.setFieldValue("skills", updatedSkills);
      notifications.show({
        title: "Skill Removed",
        message: `${skillName} has been removed from your skills`,
        color: "red",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to remove skill. Please try again.",
        color: "red",
      });
    }

    // Reset and close modal
    setSkillToDelete(null);
    closeDeleteSkillModal();
  };

  // Handle confirming experience deletion
  const confirmDeleteExperience = (index: number) => {
    setExperienceToDelete(index);
    openDeleteExperienceModal();
  };

  // Handle removing an experience
  const handleRemoveExperience = async () => {
    if (experienceToDelete === null) return;

    const index = experienceToDelete;
    if (!profile || !profile.experience[index]?._id) {
      setExperienceToDelete(null);
      closeDeleteExperienceModal();
      return;
    }

    const experienceId = profile.experience[index]._id;
    const removedExp = form.values.experience[index];

    setIsSubmitting(true);
    const success = await deleteExperience(experienceId);
    setIsSubmitting(false);

    if (success) {
      const updatedExperience = [...form.values.experience];
      updatedExperience.splice(index, 1);
      form.setFieldValue("experience", updatedExperience);
      notifications.show({
        title: "Experience Removed",
        message: `${removedExp.position} at ${removedExp.company} has been removed`,
        color: "red",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to remove experience. Please try again.",
        color: "red",
      });
    }

    // Reset and close modal
    setExperienceToDelete(null);
    closeDeleteExperienceModal();
  };

  // Handle confirming education deletion
  const confirmDeleteEducation = (index: number) => {
    setEducationToDelete(index);
    openDeleteEducationModal();
  };

  // Handle removing an education
  const handleRemoveEducation = async () => {
    if (educationToDelete === null) return;

    const index = educationToDelete;
    if (!profile || !profile.education[index]?._id) {
      setEducationToDelete(null);
      closeDeleteEducationModal();
      return;
    }

    const educationId = profile.education[index]._id;
    const removedEdu = form.values.education[index];

    setIsSubmitting(true);
    const success = await deleteEducation(educationId);
    setIsSubmitting(false);

    if (success) {
      const updatedEducation = [...form.values.education];
      updatedEducation.splice(index, 1);
      form.setFieldValue("education", updatedEducation);
      notifications.show({
        title: "Education Removed",
        message: `${removedEdu.degree} from ${removedEdu.institution} has been removed`,
        color: "red",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to remove education. Please try again.",
        color: "red",
      });
    }

    // Reset and close modal
    setEducationToDelete(null);
    closeDeleteEducationModal();
  };

  // Handle profile image upload
  const handleProfileImageChange = async (file: File | null) => {
    if (file) {
      setIsSubmitting(true);
      setProfileImage(file);

      // Create a preview for immediate UI feedback
      const imageUrl = URL.createObjectURL(file);
      setProfileImagePreview(imageUrl);

      // Upload the file to the server
      const success = await uploadResume(file);
      setIsSubmitting(false);

      if (success) {
        closeProfileImageModal();
        notifications.show({
          title: "Profile Image Updated",
          message: "Your profile image has been successfully updated",
          color: "green",
        });
      } else {
        notifications.show({
          title: "Error",
          message: "Failed to upload profile image. Please try again.",
          color: "red",
        });
      }
    }
  };

  // Handle profile image removal
  const handleRemoveProfileImage = async () => {
    setIsSubmitting(true);

    // Update profile to remove the image
    const success = await updateProfile({ resumeUrl: "" });
    setIsSubmitting(false);

    if (success) {
      setProfileImage(null);
      setProfileImagePreview(null);
      notifications.show({
        title: "Profile Image Removed",
        message: "Your profile image has been removed",
        color: "red",
      });
    } else {
      notifications.show({
        title: "Error",
        message: "Failed to remove profile image. Please try again.",
        color: "red",
      });
    }
  };

  const handleSubmit = async (values: typeof form.values) => {
    setIsSubmitting(true);

    // Prepare the data for the API
    const profileData = {
      headline: values.title,
      summary: values.bio,
      // Add phone and location fields
      phone: values.phone,
      location: values.location,
      socialLinks: {
        linkedin: values.linkedin,
        github: values.github,
        website: values.portfolio,
      },
    };

    // Update the profile without using the global loading state
    try {
      const response = await candidateApi.profile.updateProfile(profileData);

      if (response.data && response.data.success) {
        // Update the local form values to match what was saved
        form.setValues({
          ...form.values,
          title: profileData.headline || "",
          bio: profileData.summary || "",
          phone: profileData.phone || "",
          location: profileData.location || "",
          linkedin: profileData.socialLinks?.linkedin || "",
          github: profileData.socialLinks?.github || "",
          portfolio: profileData.socialLinks?.website || "",
        });

        // Refresh the profile data in the background
        refreshProfile();

        notifications.show({
          title: "Profile Updated",
          message: "Your profile has been successfully updated",
          color: "green",
          icon: <FaCheck />,
        });
      } else {
        throw new Error("Failed to update profile");
      }
    } catch (err: any) {
      console.error("Error updating profile:", err);
      notifications.show({
        title: "Error",
        message: "Failed to update profile. Please try again.",
        color: "red",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PageContainer
      breadcrumbItems={[{ title: "Home", href: "/" }, { title: "Profile" }]}
      variant="candidate"
    >
      <PageHeading
        title="My Profile"
        subtitle="Manage your personal information and preferences"
        variant="candidate"
      />

      {/* Loading state */}
      {isLoading && (
        <div className="flex h-64 w-full items-center justify-center">
          <div className="text-center">
            <FaSpinner className="mx-auto mb-4 h-8 w-8 animate-spin text-blue-500" />
            <Text size="lg" fw={500}>
              Loading your profile...
            </Text>
          </div>
        </div>
      )}

      {/* Error state */}
      {error && !isLoading && (
        <div className="flex h-64 w-full items-center justify-center">
          <div className="text-center">
            <Text c="red" size="lg" fw={500} className="mb-2">
              {error}
            </Text>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </div>
        </div>
      )}

      {/* Content when loaded */}
      {!isLoading && !error && profile && (
        <>
          {/* Hero Section with Profile Overview */}
          <Card withBorder radius="md" className="mb-8 overflow-hidden">
            <div className="relative">
              {/* Background gradient */}
              <div
                className={getThemeClasses(
                  "absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50",
                  "absolute inset-0 bg-gradient-to-r from-blue-900/20 to-indigo-900/20",
                )}
              ></div>

              <div className="relative p-6 md:p-8">
                <Grid>
                  <Grid.Col span={{ base: 12, sm: 8 }}>
                    <Group align="flex-start" className="mb-4">
                      <div className="relative">
                        <Avatar
                          size={120}
                          radius="md"
                          color="blue"
                          className={getThemeClasses(
                            "border-4 border-white shadow-md",
                            "border-4 border-dark-6 shadow-md",
                          )}
                          src={profileImagePreview}
                        >
                          {!profileImagePreview &&
                            form.values.firstName.charAt(0) +
                              form.values.lastName.charAt(0)}
                        </Avatar>
                        <div className="absolute -right-2 -bottom-2 flex gap-1">
                          <Tooltip label="Change profile picture">
                            <ActionIcon
                              className={getThemeClasses(
                                "border border-gray-200 bg-white shadow-sm",
                                "border border-gray-700 bg-dark-6 shadow-sm",
                              )}
                              radius="xl"
                              variant="filled"
                              size="md"
                              color="blue"
                              onClick={openProfileImageModal}
                            >
                              <FaCamera size={14} />
                            </ActionIcon>
                          </Tooltip>
                          {profileImagePreview && (
                            <Tooltip label="Remove profile picture">
                              <ActionIcon
                                className={getThemeClasses(
                                  "border border-gray-200 bg-white shadow-sm",
                                  "border border-gray-700 bg-dark-6 shadow-sm",
                                )}
                                radius="xl"
                                variant="filled"
                                size="md"
                                color="red"
                                onClick={handleRemoveProfileImage}
                              >
                                <FaTrash size={14} />
                              </ActionIcon>
                            </Tooltip>
                          )}
                        </div>
                      </div>

                      <div>
                        <Title order={1} className="text-2xl font-bold">
                          {form.values.firstName} {form.values.lastName}
                        </Title>

                        <Text
                          size="lg"
                          className={getThemeClasses(
                            "mb-2 text-gray-700",
                            "mb-2 text-gray-300",
                          )}
                        >
                          {form.values.title}
                        </Text>

                        <Group gap="lg" className="mt-3">
                          <Group gap="xs">
                            <FaEnvelope className="text-blue-500" />
                            <Text size="sm">{form.values.email}</Text>
                          </Group>

                          <Group gap="xs">
                            <FaPhone className="text-blue-500" />
                            <Text size="sm">
                              {form.values.phone || "Add your phone number"}
                            </Text>
                          </Group>

                          <Group gap="xs">
                            <FaMapMarkerAlt className="text-blue-500" />
                            <Text size="sm">
                              {form.values.location || "Add your location"}
                            </Text>
                          </Group>
                        </Group>
                      </div>
                    </Group>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, sm: 4 }}>
                    <Card
                      withBorder
                      radius="md"
                      className={getThemeClasses(
                        "bg-white shadow-sm",
                        "bg-dark-7 shadow-sm border-dark-4",
                      )}
                    >
                      <Text fw={700} ta="center" className="mb-2">
                        Profile Completion
                      </Text>
                      <Group justify="center" className="mb-2">
                        <RingProgress
                          size={80}
                          thickness={8}
                          roundCaps
                          sections={[
                            {
                              value: profileCompletionPercentage,
                              color: "blue",
                            },
                          ]}
                          label={
                            <Text ta="center" size="sm" fw={700}>
                              {profileCompletionPercentage}%
                            </Text>
                          }
                        />
                      </Group>
                      <Text size="sm" c="dimmed" ta="center">
                        Complete your profile to increase visibility to
                        employers
                      </Text>
                    </Card>
                  </Grid.Col>
                </Grid>

                {/* Quick Action Buttons */}
                <Group className="mt-4">
                  <Group ml="auto" gap="xs">
                    <Tooltip label="LinkedIn Profile">
                      <ActionIcon
                        component={Link}
                        to={form.values.linkedin}
                        target="_blank"
                        variant="light"
                        color="blue"
                        size="lg"
                      >
                        <FaLinkedin size={20} />
                      </ActionIcon>
                    </Tooltip>

                    <Tooltip label="GitHub Profile">
                      <ActionIcon
                        component={Link}
                        to={form.values.github}
                        target="_blank"
                        variant="light"
                        color="dark"
                        size="lg"
                      >
                        <FaGithub size={20} />
                      </ActionIcon>
                    </Tooltip>

                    <Tooltip label="Portfolio">
                      <ActionIcon
                        component={Link}
                        to={form.values.portfolio}
                        target="_blank"
                        variant="light"
                        color="indigo"
                        size="lg"
                      >
                        <FaGlobe size={20} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Group>
              </div>
            </div>
          </Card>

          {/* Tabbed Content */}
          <form onSubmit={form.onSubmit(handleSubmit)}>
            <Tabs
              value={activeTab}
              onChange={setActiveTab}
              className="mb-6"
              styles={{
                tab: {
                  color: isDark
                    ? "var(--mantine-color-gray-4) !important"
                    : undefined,
                  "&[data-active]": {
                    color: isDark
                      ? "var(--mantine-color-blue-4) !important"
                      : undefined,
                    borderColor: isDark
                      ? "var(--mantine-color-blue-4) !important"
                      : undefined,
                  },
                },
              }}
            >
              <Tabs.List grow>
                <Tabs.Tab value="personal" leftSection={<FaUser size={16} />}>
                  Personal Info
                </Tabs.Tab>
                <Tabs.Tab value="skills" leftSection={<FaStar size={16} />}>
                  Skills
                </Tabs.Tab>
                <Tabs.Tab
                  value="experience"
                  leftSection={<FaBriefcase size={16} />}
                >
                  Experience
                </Tabs.Tab>
                <Tabs.Tab
                  value="education"
                  leftSection={<FaGraduationCap size={16} />}
                >
                  Education
                </Tabs.Tab>
              </Tabs.List>

              {/* Personal Information Tab */}
              <Tabs.Panel value="personal" pt="md">
                <Card withBorder radius="md" className="p-6 shadow-sm">
                  <Title
                    order={3}
                    className={getThemeClasses(
                      "mb-4 text-xl font-semibold text-blue-800",
                      "mb-4 text-xl font-semibold text-blue-400",
                    )}
                  >
                    Personal Information
                  </Title>

                  <Grid gutter="md">
                    <Grid.Col span={{ base: 12, sm: 6 }}>
                      <TextInput
                        label="First Name"
                        placeholder="Your first name"
                        required
                        leftSection={
                          <FaUser size={16} className="text-gray-500" />
                        }
                        {...form.getInputProps("firstName")}
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6 }}>
                      <TextInput
                        label="Last Name"
                        placeholder="Your last name"
                        required
                        leftSection={
                          <FaUser size={16} className="text-gray-500" />
                        }
                        {...form.getInputProps("lastName")}
                      />
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <TextInput
                        label="Email"
                        placeholder="Your email"
                        required
                        leftSection={
                          <FaEnvelope size={16} className="text-gray-500" />
                        }
                        {...form.getInputProps("email")}
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6 }}>
                      <TextInput
                        label="Phone"
                        placeholder="Your phone number"
                        leftSection={
                          <FaPhone size={16} className="text-gray-500" />
                        }
                        {...form.getInputProps("phone")}
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6 }}>
                      <TextInput
                        label="Location"
                        placeholder="City, State"
                        leftSection={
                          <FaMapMarkerAlt size={16} className="text-gray-500" />
                        }
                        {...form.getInputProps("location")}
                      />
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <TextInput
                        label="Professional Title"
                        placeholder="e.g. Senior Frontend Developer"
                        leftSection={
                          <FaBriefcase size={16} className="text-gray-500" />
                        }
                        {...form.getInputProps("title")}
                      />
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Textarea
                        label="Bio"
                        placeholder="Tell employers about yourself"
                        minRows={4}
                        {...form.getInputProps("bio")}
                      />
                    </Grid.Col>
                  </Grid>

                  <Divider my="lg" />

                  <Title
                    order={3}
                    className={getThemeClasses(
                      "mb-4 text-xl font-semibold text-blue-800",
                      "mb-4 text-xl font-semibold text-blue-400",
                    )}
                  >
                    Social Links
                  </Title>

                  <Grid gutter="md">
                    <Grid.Col span={{ base: 12, sm: 6 }}>
                      <TextInput
                        label="LinkedIn"
                        placeholder="https://linkedin.com/in/username"
                        leftSection={
                          <FaLinkedin size={16} className="text-blue-500" />
                        }
                        {...form.getInputProps("linkedin")}
                      />
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, sm: 6 }}>
                      <TextInput
                        label="GitHub"
                        placeholder="https://github.com/username"
                        leftSection={
                          <FaGithub size={16} className="text-gray-700" />
                        }
                        {...form.getInputProps("github")}
                      />
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <TextInput
                        label="Portfolio Website"
                        placeholder="https://yourportfolio.com"
                        leftSection={
                          <FaGlobe size={16} className="text-indigo-500" />
                        }
                        {...form.getInputProps("portfolio")}
                      />
                    </Grid.Col>
                  </Grid>
                </Card>
              </Tabs.Panel>

              {/* Skills Tab */}
              <Tabs.Panel value="skills" pt="md">
                <Card withBorder radius="md" className="p-6 shadow-sm">
                  <Title
                    order={3}
                    className={getThemeClasses(
                      "mb-4 text-xl font-semibold text-blue-800",
                      "mb-4 text-xl font-semibold text-blue-400",
                    )}
                  >
                    Skills & Expertise
                  </Title>

                  <Text
                    className={getThemeClasses(
                      "mb-4 text-gray-600",
                      "mb-4 text-gray-400",
                    )}
                  >
                    Add your key skills to help employers find you. Highlight
                    your strongest skills first.
                  </Text>

                  {form.values.skills.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FaStar size={40} className="mb-3 text-gray-300" />
                      <Text size="lg" fw={500} c="dimmed" className="mb-2">
                        No skills added yet
                      </Text>
                      <Text size="sm" c="dimmed" className="mb-4 max-w-md">
                        Add your professional skills to showcase your expertise
                        to potential employers
                      </Text>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3">
                      {form.values.skills.map((skill, index) => {
                        // Get skill name and level
                        const skillName =
                          typeof skill === "string" ? skill : skill.name;
                        const skillLevel =
                          typeof skill === "string"
                            ? index < 2
                              ? "expert"
                              : index < 4
                                ? "advanced"
                                : "intermediate"
                            : skill.level;

                        // Format level for display
                        const displayLevel =
                          skillLevel.charAt(0).toUpperCase() +
                          skillLevel.slice(1);

                        // Determine color based on level
                        const color =
                          skillLevel === "expert"
                            ? "green"
                            : skillLevel === "advanced"
                              ? "blue"
                              : skillLevel === "intermediate"
                                ? "cyan"
                                : "gray";

                        // Determine dots count based on level
                        const dotsCount =
                          skillLevel === "expert"
                            ? 5
                            : skillLevel === "advanced"
                              ? 4
                              : skillLevel === "intermediate"
                                ? 3
                                : 2;

                        return (
                          <Card
                            key={index}
                            withBorder
                            p="sm"
                            radius="md"
                            className="relative transition-all duration-200 hover:shadow-md"
                          >
                            <div className="absolute right-2 top-2">
                              <ActionIcon
                                variant="light"
                                color="red"
                                size="sm"
                                radius="xl"
                                onClick={() => confirmDeleteSkill(index)}
                                className="opacity-70 transition-opacity hover:opacity-100"
                              >
                                <FaTrash size={12} />
                              </ActionIcon>
                            </div>

                            <div className="mb-2">
                              <Text
                                fw={600}
                                size="md"
                                className="mr-8 truncate"
                              >
                                {skillName}
                              </Text>
                            </div>

                            <Group justify="space-between" align="center">
                              <Badge
                                color={color}
                                variant="light"
                                size="sm"
                                radius="sm"
                              >
                                {displayLevel}
                              </Badge>

                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <div
                                    key={i}
                                    className={`h-2 w-2 rounded-full mx-0.5 ${
                                      i < dotsCount
                                        ? `bg-${color}-500`
                                        : "bg-gray-200"
                                    }`}
                                  />
                                ))}
                              </div>
                            </Group>
                          </Card>
                        );
                      })}
                    </div>
                  )}

                  <Group mt="xl">
                    <Button
                      leftSection={<FaPlus size={16} />}
                      variant="light"
                      onClick={openSkillModal}
                      fullWidth
                    >
                      Add New Skill
                    </Button>
                  </Group>

                  {/* Skill Modal */}
                  <Modal
                    opened={skillModalOpened}
                    onClose={closeSkillModal}
                    title="Add New Skill"
                    centered
                  >
                    <TextInput
                      label="Skill Name"
                      placeholder="e.g. React, JavaScript, UI Design"
                      required
                      value={newSkill}
                      onChange={(e) => {
                        setNewSkill(e.target.value);
                        if (skillError) setSkillError("");
                      }}
                      error={skillError}
                      className="mb-4"
                    />
                    <Select
                      label="Proficiency Level"
                      placeholder="Select your proficiency level"
                      data={[
                        { value: "expert", label: "Expert" },
                        { value: "advanced", label: "Advanced" },
                        { value: "intermediate", label: "Intermediate" },
                        { value: "beginner", label: "Beginner" },
                      ]}
                      value={skillLevel}
                      onChange={(value) =>
                        setSkillLevel(value || "intermediate")
                      }
                      className="mb-6"
                    />
                    <Group justify="flex-end">
                      <Button variant="default" onClick={closeSkillModal}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleAddSkill}
                        loading={isAddingSkill}
                        leftSection={!isAddingSkill && <FaPlus size={14} />}
                      >
                        {isAddingSkill ? "" : "Add Skill"}
                      </Button>
                    </Group>
                  </Modal>
                </Card>
              </Tabs.Panel>

              {/* Experience Tab */}
              <Tabs.Panel value="experience" pt="md">
                <Card withBorder radius="md" className="p-6 shadow-sm">
                  <Title
                    order={3}
                    className={getThemeClasses(
                      "mb-4 text-xl font-semibold text-blue-800",
                      "mb-4 text-xl font-semibold text-blue-400",
                    )}
                  >
                    Work Experience
                  </Title>

                  {form.values.experience.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FaBriefcase size={40} className="mb-3 text-gray-300" />
                      <Text size="lg" fw={500} c="dimmed" className="mb-2">
                        No work experience added yet
                      </Text>
                      <Text size="sm" c="dimmed" className="mb-4 max-w-md">
                        Add your professional experience to showcase your career
                        journey to potential employers
                      </Text>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 gap-4">
                      {form.values.experience.map((exp, index) => (
                        <Card
                          key={index}
                          withBorder
                          radius="md"
                          className="relative overflow-hidden transition-all duration-200 hover:shadow-md"
                        >
                          <div className="absolute right-3 top-3">
                            <ActionIcon
                              variant="light"
                              color="red"
                              radius="xl"
                              onClick={() => confirmDeleteExperience(index)}
                              className="opacity-70 transition-opacity hover:opacity-100"
                            >
                              <FaTrash size={14} />
                            </ActionIcon>
                          </div>

                          <div
                            className={`absolute left-0 top-0 h-full w-1 ${index % 3 === 0 ? "bg-blue-500" : index % 3 === 1 ? "bg-indigo-500" : "bg-violet-500"}`}
                          ></div>

                          <div className="pl-4">
                            <Group align="center" className="mb-1">
                              <div
                                className={`flex h-10 w-10 items-center justify-center rounded-full ${index % 3 === 0 ? "bg-blue-100" : index % 3 === 1 ? "bg-indigo-100" : "bg-violet-100"}`}
                              >
                                <FaBriefcase
                                  className={`${index % 3 === 0 ? "text-blue-500" : index % 3 === 1 ? "text-indigo-500" : "text-violet-500"}`}
                                  size={18}
                                />
                              </div>
                              <div>
                                <Title
                                  order={4}
                                  className="text-lg font-semibold"
                                >
                                  {exp.position}
                                </Title>
                              </div>
                            </Group>

                            <div className="ml-12 mb-3">
                              <Group className="mb-1">
                                <Group gap="xs">
                                  <FaBuilding
                                    className="text-gray-500"
                                    size={14}
                                  />
                                  <Text fw={500} c="dimmed">
                                    {exp.company}
                                  </Text>
                                </Group>
                                <Divider orientation="vertical" />
                                <Group gap="xs">
                                  <FaCalendarAlt
                                    className="text-gray-500"
                                    size={14}
                                  />
                                  <Text size="sm" c="dimmed">
                                    {exp.duration}
                                  </Text>
                                </Group>
                              </Group>

                              {exp.description && (
                                <div className="mt-2">
                                  <Text
                                    className={getThemeClasses(
                                      "text-gray-700",
                                      "text-gray-300",
                                    )}
                                  >
                                    {exp.description}
                                  </Text>
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}

                  <Button
                    leftSection={<FaPlus size={16} />}
                    variant="outline"
                    color="blue"
                    className="mt-4"
                    onClick={openExperienceModal}
                  >
                    Add Experience
                  </Button>

                  {/* Experience Modal */}
                  <Modal
                    opened={experienceModalOpened}
                    onClose={closeExperienceModal}
                    title="Add Work Experience"
                    centered
                    size="lg"
                  >
                    <Grid gutter="md">
                      <Grid.Col span={12}>
                        <TextInput
                          label="Position"
                          placeholder="e.g. Senior Frontend Developer"
                          required
                          value={newExperience.position}
                          onChange={(e) => {
                            setNewExperience({
                              ...newExperience,
                              position: e.target.value,
                            });
                            if (experienceErrors.position) {
                              setExperienceErrors({
                                ...experienceErrors,
                                position: "",
                              });
                            }
                          }}
                          error={experienceErrors.position}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <TextInput
                          label="Company"
                          placeholder="e.g. Tech Solutions Inc."
                          required
                          value={newExperience.company}
                          onChange={(e) => {
                            setNewExperience({
                              ...newExperience,
                              company: e.target.value,
                            });
                            if (experienceErrors.company) {
                              setExperienceErrors({
                                ...experienceErrors,
                                company: "",
                              });
                            }
                          }}
                          error={experienceErrors.company}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <TextInput
                          type="number"
                          label="Start Year"
                          placeholder="e.g. 2020"
                          required
                          value={
                            newExperience.startDate
                              ? newExperience.startDate.getFullYear().toString()
                              : ""
                          }
                          onChange={(e) => {
                            const year = parseInt(e.target.value);
                            if (!isNaN(year)) {
                              const date = new Date(year, 0, 1);
                              setNewExperience({
                                ...newExperience,
                                startDate: date,
                              });
                            } else {
                              setNewExperience({
                                ...newExperience,
                                startDate: null,
                              });
                            }
                            if (experienceErrors.startDate) {
                              setExperienceErrors({
                                ...experienceErrors,
                                startDate: "",
                              });
                            }
                          }}
                          error={experienceErrors.startDate}
                          min={1900}
                          max={new Date().getFullYear()}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <TextInput
                          type="number"
                          label="End Year"
                          placeholder="e.g. 2023"
                          value={
                            newExperience.current
                              ? ""
                              : newExperience.endDate
                                ? newExperience.endDate.getFullYear().toString()
                                : ""
                          }
                          onChange={(e) => {
                            const year = parseInt(e.target.value);
                            if (!isNaN(year)) {
                              const date = new Date(year, 11, 31);
                              setNewExperience({
                                ...newExperience,
                                endDate: date,
                                current: false,
                              });
                            } else {
                              setNewExperience({
                                ...newExperience,
                                endDate: null,
                                current: false,
                              });
                            }
                            if (experienceErrors.endDate) {
                              setExperienceErrors({
                                ...experienceErrors,
                                endDate: "",
                              });
                            }
                          }}
                          error={experienceErrors.endDate}
                          disabled={newExperience.current}
                          min={
                            newExperience.startDate
                              ? newExperience.startDate.getFullYear()
                              : 1900
                          }
                          max={new Date().getFullYear()}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Checkbox
                          label="I currently work here"
                          checked={newExperience.current}
                          onChange={(e) => {
                            setNewExperience({
                              ...newExperience,
                              current: e.currentTarget.checked,
                              endDate: e.currentTarget.checked
                                ? null
                                : newExperience.endDate,
                            });
                            if (experienceErrors.endDate) {
                              setExperienceErrors({
                                ...experienceErrors,
                                endDate: "",
                              });
                            }
                          }}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Textarea
                          label="Description"
                          placeholder="Describe your responsibilities and achievements"
                          minRows={3}
                          value={newExperience.description}
                          onChange={(e) => {
                            setNewExperience({
                              ...newExperience,
                              description: e.target.value,
                            });
                            if (experienceErrors.description) {
                              setExperienceErrors({
                                ...experienceErrors,
                                description: "",
                              });
                            }
                          }}
                          error={experienceErrors.description}
                        />
                      </Grid.Col>
                    </Grid>
                    <Group justify="flex-end" mt="xl">
                      <Button variant="default" onClick={closeExperienceModal}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleAddExperience}
                        loading={isAddingExperience}
                        leftSection={
                          !isAddingExperience && <FaPlus size={14} />
                        }
                      >
                        {isAddingExperience ? "" : "Add Experience"}
                      </Button>
                    </Group>
                  </Modal>
                </Card>
              </Tabs.Panel>

              {/* Education Tab */}
              <Tabs.Panel value="education" pt="md">
                <Card withBorder radius="md" className="p-6 shadow-sm">
                  <Title
                    order={3}
                    className={getThemeClasses(
                      "mb-4 text-xl font-semibold text-blue-800",
                      "mb-4 text-xl font-semibold text-blue-400",
                    )}
                  >
                    Education
                  </Title>

                  {form.values.education.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <FaGraduationCap
                        size={40}
                        className="mb-3 text-gray-300"
                      />
                      <Text size="lg" fw={500} c="dimmed" className="mb-2">
                        No education added yet
                      </Text>
                      <Text size="sm" c="dimmed" className="mb-4 max-w-md">
                        Add your educational background to showcase your
                        qualifications to potential employers
                      </Text>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 gap-4">
                      {form.values.education.map((edu, index) => (
                        <Card
                          key={index}
                          withBorder
                          radius="md"
                          className="relative overflow-hidden transition-all duration-200 hover:shadow-md"
                        >
                          <div className="absolute right-3 top-3">
                            <ActionIcon
                              variant="light"
                              color="red"
                              radius="xl"
                              onClick={() => confirmDeleteEducation(index)}
                              className="opacity-70 transition-opacity hover:opacity-100"
                            >
                              <FaTrash size={14} />
                            </ActionIcon>
                          </div>

                          <div
                            className={`absolute left-0 top-0 h-full w-1 ${index % 3 === 0 ? "bg-indigo-500" : index % 3 === 1 ? "bg-purple-500" : "bg-violet-500"}`}
                          ></div>

                          <div className="pl-4">
                            <Group align="center" className="mb-1">
                              <div
                                className={`flex h-10 w-10 items-center justify-center rounded-full ${index % 3 === 0 ? "bg-indigo-100" : index % 3 === 1 ? "bg-purple-100" : "bg-violet-100"}`}
                              >
                                <FaGraduationCap
                                  className={`${index % 3 === 0 ? "text-indigo-500" : index % 3 === 1 ? "text-purple-500" : "text-violet-500"}`}
                                  size={18}
                                />
                              </div>
                              <div>
                                <Title
                                  order={4}
                                  className="text-lg font-semibold"
                                >
                                  {edu.degree}
                                </Title>
                              </div>
                            </Group>

                            <div className="ml-12 mb-2">
                              <Group className="mb-1">
                                <Group gap="xs">
                                  <FaBuilding
                                    className="text-gray-500"
                                    size={14}
                                  />
                                  <Text fw={500} c="dimmed">
                                    {edu.institution}
                                  </Text>
                                </Group>
                                <Divider orientation="vertical" />
                                <Group gap="xs">
                                  <FaCalendarAlt
                                    className="text-gray-500"
                                    size={14}
                                  />
                                  <Text size="sm" c="dimmed">
                                    {edu.year}
                                  </Text>
                                </Group>
                              </Group>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}

                  <Button
                    leftSection={<FaPlus size={16} />}
                    variant="outline"
                    color="blue"
                    className="mt-4"
                    onClick={openEducationModal}
                  >
                    Add Education
                  </Button>

                  {/* Education Modal */}
                  <Modal
                    opened={educationModalOpened}
                    onClose={closeEducationModal}
                    title="Add Education"
                    centered
                    size="lg"
                  >
                    <Grid gutter="md">
                      <Grid.Col span={12}>
                        <TextInput
                          label="Degree"
                          placeholder="e.g. Bachelor of Science in Computer Science"
                          required
                          value={newEducation.degree}
                          onChange={(e) => {
                            setNewEducation({
                              ...newEducation,
                              degree: e.target.value,
                            });
                            if (educationErrors.degree) {
                              setEducationErrors({
                                ...educationErrors,
                                degree: "",
                              });
                            }
                          }}
                          error={educationErrors.degree}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <TextInput
                          label="Institution"
                          placeholder="e.g. University of Technology"
                          required
                          value={newEducation.institution}
                          onChange={(e) => {
                            setNewEducation({
                              ...newEducation,
                              institution: e.target.value,
                            });
                            if (educationErrors.institution) {
                              setEducationErrors({
                                ...educationErrors,
                                institution: "",
                              });
                            }
                          }}
                          error={educationErrors.institution}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <TextInput
                          type="number"
                          label="Year"
                          placeholder="e.g. 2018"
                          description="Enter the year you started this education"
                          required
                          value={newEducation.year}
                          onChange={(e) => {
                            setNewEducation({
                              ...newEducation,
                              year: e.target.value,
                            });
                            if (educationErrors.year) {
                              setEducationErrors({
                                ...educationErrors,
                                year: "",
                              });
                            }
                          }}
                          error={educationErrors.year}
                          min={1900}
                          max={new Date().getFullYear()}
                        />
                      </Grid.Col>
                    </Grid>
                    <Group justify="flex-end" mt="xl">
                      <Button variant="default" onClick={closeEducationModal}>
                        Cancel
                      </Button>
                      <Button
                        onClick={handleAddEducation}
                        loading={isAddingEducation}
                        leftSection={!isAddingEducation && <FaPlus size={14} />}
                      >
                        {isAddingEducation ? "" : "Add Education"}
                      </Button>
                    </Group>
                  </Modal>
                </Card>
              </Tabs.Panel>
            </Tabs>

            {/* Only show Save All Changes button for Personal Info tab */}
            {activeTab === "personal" && (
              <Group justify="flex-end" mt="xl">
                <Button
                  type="submit"
                  size="md"
                  loading={isSubmitting}
                  leftSection={!isSubmitting && <FaSave size={16} />}
                >
                  {isSubmitting ? "" : "Save All Changes"}
                </Button>
              </Group>
            )}
          </form>

          {/* Profile Image Modal */}
          <Modal
            opened={profileImageModalOpened}
            onClose={closeProfileImageModal}
            title="Update Profile Picture"
            centered
          >
            <div className="mb-6 flex flex-col items-center">
              <Avatar
                size={150}
                radius="md"
                color="blue"
                className="mb-4 border-4 border-white shadow-md"
                src={profileImagePreview}
              >
                {!profileImagePreview &&
                  form.values.firstName.charAt(0) +
                    form.values.lastName.charAt(0)}
              </Avatar>

              <FileInput
                label="Upload new profile picture"
                placeholder="Click to select an image"
                accept="image/png,image/jpeg,image/jpg"
                className="mb-4 w-full"
                onChange={handleProfileImageChange}
                leftSection={<FaUpload size={16} />}
                clearable
              />

              <Text size="sm" c="dimmed" className="mb-4 text-center">
                Recommended: Square image, at least 400x400 pixels
              </Text>

              <Group justify="center" className="w-full">
                <Button variant="default" onClick={closeProfileImageModal}>
                  Cancel
                </Button>
                {profileImagePreview && (
                  <Button color="red" onClick={handleRemoveProfileImage}>
                    Remove Photo
                  </Button>
                )}
              </Group>
            </div>
          </Modal>

          {/* Delete Confirmation Modals */}
          <Modal
            opened={deleteSkillModalOpened}
            onClose={closeDeleteSkillModal}
            title="Confirm Deletion"
            centered
            size="sm"
          >
            <Text size="sm" mb="lg">
              Are you sure you want to delete this skill? This action cannot be
              undone.
            </Text>
            <Group justify="flex-end">
              <Button variant="default" onClick={closeDeleteSkillModal}>
                Cancel
              </Button>
              <Button
                color="red"
                onClick={handleRemoveSkill}
                loading={isSubmitting}
              >
                {isSubmitting ? "" : "Delete Skill"}
              </Button>
            </Group>
          </Modal>

          <Modal
            opened={deleteExperienceModalOpened}
            onClose={closeDeleteExperienceModal}
            title="Confirm Deletion"
            centered
            size="sm"
          >
            <Text size="sm" mb="lg">
              Are you sure you want to delete this experience? This action
              cannot be undone.
            </Text>
            <Group justify="flex-end">
              <Button variant="default" onClick={closeDeleteExperienceModal}>
                Cancel
              </Button>
              <Button
                color="red"
                onClick={handleRemoveExperience}
                loading={isSubmitting}
              >
                {isSubmitting ? "" : "Delete Experience"}
              </Button>
            </Group>
          </Modal>

          <Modal
            opened={deleteEducationModalOpened}
            onClose={closeDeleteEducationModal}
            title="Confirm Deletion"
            centered
            size="sm"
          >
            <Text size="sm" mb="lg">
              Are you sure you want to delete this education? This action cannot
              be undone.
            </Text>
            <Group justify="flex-end">
              <Button variant="default" onClick={closeDeleteEducationModal}>
                Cancel
              </Button>
              <Button
                color="red"
                onClick={handleRemoveEducation}
                loading={isSubmitting}
              >
                {isSubmitting ? "" : "Delete Education"}
              </Button>
            </Group>
          </Modal>
        </>
      )}
    </PageContainer>
  );
}
