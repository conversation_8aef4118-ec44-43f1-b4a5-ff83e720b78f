export type UserRole = "candidate" | "employer" | "admin";

export interface User {
  id: number;
  email: string;
  name: string;
  role: UserR<PERSON>;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  accessToken: string | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  professionalTitle: string;
  email: string;
  password: string;
  passwordConfirmation: string;
  role: UserRole;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
}
